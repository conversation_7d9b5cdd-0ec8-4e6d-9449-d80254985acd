#!/usr/bin/env python3
"""
LangExtract API 测试脚本
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查端点"""
    print("🔍 测试健康检查...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_simple_extract():
    """测试简单提取端点"""
    print("🔍 测试简单提取...")
    
    data = {
        "text": "苹果公司的CEO蒂姆·库克今天宣布了新的iPhone 15产品。这款产品将在9月份发布。",
        "prompt": "提取人名、公司名和产品名",
        "model_id": "gemini-2.5-flash"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/extract/simple", json=data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 提取成功!")
            print(f"提取结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 提取失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    print()

def test_advanced_extract():
    """测试高级提取端点"""
    print("🔍 测试高级提取...")
    
    data = {
        "text": "苹果公司的CEO蒂姆·库克今天宣布了新的iPhone 15产品。",
        "prompt_description": "提取人名、公司名和产品名，并标注其类型和角色",
        "examples": [
            {
                "text": "微软公司的创始人比尔·盖茨推出了Windows操作系统。",
                "extractions": [
                    {
                        "extraction_class": "person",
                        "extraction_text": "比尔·盖茨",
                        "attributes": {"role": "创始人", "company": "微软"}
                    },
                    {
                        "extraction_class": "company", 
                        "extraction_text": "微软公司",
                        "attributes": {"type": "科技公司", "industry": "软件"}
                    },
                    {
                        "extraction_class": "product",
                        "extraction_text": "Windows操作系统",
                        "attributes": {"category": "软件", "type": "操作系统"}
                    }
                ]
            }
        ],
        "model_id": "gemini-2.5-flash"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/extract", json=data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 高级提取成功!")
            print(f"提取结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 高级提取失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    print()

def main():
    print("🚀 开始测试 LangExtract API")
    print("=" * 50)
    
    # 检查API密钥
    api_key = os.getenv("LANGEXTRACT_API_KEY")
    if not api_key:
        print("⚠️  警告: 未设置 LANGEXTRACT_API_KEY 环境变量")
        print("   某些测试可能会失败")
    else:
        print("✅ 检测到 API 密钥")
    print()
    
    # 运行测试
    test_health()
    test_simple_extract()
    test_advanced_extract()
    
    print("🎉 测试完成!")

if __name__ == "__main__":
    main()
