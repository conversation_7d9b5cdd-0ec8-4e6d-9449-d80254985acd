# LangExtract API Service

基于Google LangExtract的结构化信息提取API服务，可以部署在Dokploy上。

## 功能特性

- 🚀 基于FastAPI的高性能API服务
- 🤖 支持Google Gemini模型进行文本信息提取
- 📊 结构化数据输出，支持自定义提取规则
- 🐳 Docker容器化部署
- 🔧 完整的健康检查和错误处理
- 📝 自动生成的API文档

## 快速开始

### 1. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，添加你的Gemini API密钥
# 获取API密钥: https://aistudio.google.com/app/apikey
```

### 2. 本地开发

```bash
# 安装依赖
cd app
pip install -r requirements.txt

# 启动开发服务器
python main.py
```

### 3. Docker部署

```bash
# 构建并启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

## API使用示例

### 基础提取接口

```bash
curl -X POST "http://localhost:8000/extract/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "苹果公司的CEO蒂姆·库克今天宣布了新的iPhone产品。",
    "prompt": "提取人名、公司名和产品名",
    "model_id": "gemini-2.5-flash"
  }'
```

### 高级提取接口

```bash
curl -X POST "http://localhost:8000/extract" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "苹果公司的CEO蒂姆·库克今天宣布了新的iPhone产品。",
    "prompt_description": "提取人名、公司名和产品名，并标注其类型",
    "examples": [
      {
        "text": "微软公司的创始人比尔·盖茨推出了Windows操作系统。",
        "extractions": [
          {
            "extraction_class": "person",
            "extraction_text": "比尔·盖茨",
            "attributes": {"role": "创始人"}
          },
          {
            "extraction_class": "company", 
            "extraction_text": "微软公司",
            "attributes": {"type": "科技公司"}
          },
          {
            "extraction_class": "product",
            "extraction_text": "Windows操作系统",
            "attributes": {"category": "软件"}
          }
        ]
      }
    ],
    "model_id": "gemini-2.5-flash"
  }'
```

## 部署到Dokploy

1. 在Dokploy中创建新的应用
2. 连接到你的Git仓库
3. 设置环境变量 `LANGEXTRACT_API_KEY`
4. 配置端口映射 `8000:8000`
5. 部署应用

## API文档

启动服务后，访问以下地址查看完整的API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 健康检查

```bash
curl http://localhost:8000/health
```

## 支持的模型

- `gemini-2.5-flash` (推荐，速度快，成本低)
- `gemini-2.5-pro` (复杂任务，推理能力强)

## 注意事项

- 使用Gemini模型需要有效的API密钥
- 建议在生产环境中使用Tier 2 Gemini配额以提高吞吐量
- 请遵守Google的使用条款和限制

## 许可证

本项目基于Apache 2.0许可证开源。
