# 部署指南

## 在Dokploy上部署LangExtract API

### 前置要求

1. 一个Dokploy实例
2. Google Gemini API密钥 (从 https://aistudio.google.com/app/apikey 获取)
3. Git仓库 (GitHub, GitLab等)

### 部署步骤

#### 1. 准备代码仓库

确保你的代码仓库包含以下文件：
- `Dockerfile`
- `docker-compose.yml`
- `app/main.py`
- `app/requirements.txt`
- `.env.example`

#### 2. 在Dokploy中创建应用

1. 登录Dokploy控制台
2. 点击 "Create Application"
3. 选择 "Docker Compose" 类型
4. 连接到你的Git仓库

#### 3. 配置环境变量

在Dokploy的环境变量设置中添加：

```
LANGEXTRACT_API_KEY=your-actual-gemini-api-key-here
LOG_LEVEL=INFO
```

#### 4. 配置端口映射

- 容器端口: `8000`
- 主机端口: `8000` (或其他可用端口)

#### 5. 部署配置

- 构建上下文: `/` (根目录)
- Dockerfile路径: `./Dockerfile`
- Docker Compose文件: `./docker-compose.yml`

#### 6. 部署应用

1. 点击 "Deploy" 按钮
2. 等待构建和部署完成
3. 检查部署日志确保没有错误

### 验证部署

部署完成后，访问以下端点验证服务：

1. **健康检查**: `GET https://your-domain.com/health`
2. **API文档**: `GET https://your-domain.com/docs`
3. **根端点**: `GET https://your-domain.com/`

### 测试API

使用curl或Postman测试API：

```bash
# 健康检查
curl https://your-domain.com/health

# 简单提取测试
curl -X POST "https://your-domain.com/extract/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "苹果公司的CEO蒂姆·库克宣布了新产品。",
    "prompt": "提取人名和公司名"
  }'
```

### 监控和日志

1. **查看日志**: 在Dokploy控制台中查看应用日志
2. **健康检查**: 服务包含自动健康检查
3. **错误监控**: 检查日志中的错误信息

### 故障排除

#### 常见问题

1. **API密钥错误**
   - 检查环境变量 `LANGEXTRACT_API_KEY` 是否正确设置
   - 确认API密钥有效且有足够配额

2. **构建失败**
   - 检查Dockerfile语法
   - 确认requirements.txt中的依赖版本兼容

3. **服务无法启动**
   - 检查端口配置
   - 查看容器日志获取详细错误信息

4. **API请求失败**
   - 检查请求格式是否正确
   - 确认模型ID有效 (推荐使用 `gemini-2.5-flash`)

#### 调试命令

```bash
# 查看容器状态
docker ps

# 查看容器日志
docker logs langextract-api

# 进入容器调试
docker exec -it langextract-api /bin/bash

# 测试健康检查
curl http://localhost:8000/health
```

### 性能优化

1. **并发设置**: 根据需要调整 `max_workers` 参数
2. **缓存**: 考虑添加Redis缓存常用提取结果
3. **负载均衡**: 对于高流量，考虑部署多个实例
4. **监控**: 设置性能监控和告警

### 安全建议

1. **API密钥安全**: 
   - 不要在代码中硬编码API密钥
   - 使用环境变量管理敏感信息
   - 定期轮换API密钥

2. **网络安全**:
   - 配置适当的CORS策略
   - 使用HTTPS
   - 考虑添加API认证

3. **资源限制**:
   - 设置合理的请求大小限制
   - 配置超时设置
   - 监控API使用量

### 扩展功能

1. **批量处理**: 添加批量文本处理端点
2. **结果缓存**: 实现提取结果缓存机制
3. **用户认证**: 添加API密钥或JWT认证
4. **速率限制**: 实现API调用频率限制
5. **数据持久化**: 添加数据库存储提取历史
