from fastapi import FastAPI
from pydantic import BaseModel
from typing import List, Union, Dict
import langextract as lx
import os

app = FastAPI()

# ✅ examples 可以是 str 或 dict
class ExtractRequest(BaseModel):
    text: str
    prompt: str
    examples: List[Union[str, Dict[str, str]]]

@app.post("/extract")
async def extract_entities(req: ExtractRequest):
    def normalize_example(ex):
        """确保 example 是 dict，符合 langextract.ExampleData(**dict) 要求"""
        if isinstance(ex, str):
            return {"text": ex}
        return ex  # 已经是 dict

    # ✅ 转换 examples
    normalized_examples = [normalize_example(e) for e in req.examples]

    results = []
    for example in normalized_examples:
        result = lx.extract(req.prompt, example)
        results.append(result)

    # ✅ 处理主文本
    main_result = lx.extract(req.prompt, {"text": req.text})

    return {
        "main_text_result": main_result,
        "examples_results": results
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000)
