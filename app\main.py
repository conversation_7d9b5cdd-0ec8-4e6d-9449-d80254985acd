from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
import langextract as lx
import os
from dotenv import load_dotenv
import logging

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="LangExtract API",
    description="A FastAPI service for structured information extraction using LangExtract",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class ExtractionData(BaseModel):
    extraction_class: str = Field(..., description="The class/type of the extraction")
    extraction_text: str = Field(..., description="The exact text being extracted")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="Additional attributes for the extraction")

class ExampleData(BaseModel):
    text: str = Field(..., description="Example text")
    extractions: List[ExtractionData] = Field(..., description="List of extractions for this example")

class ExtractRequest(BaseModel):
    text: str = Field(..., description="The text to extract information from")
    prompt_description: str = Field(..., description="Description of what to extract")
    examples: List[ExampleData] = Field(..., description="Examples to guide the extraction")
    model_id: str = Field(default="gemini-2.5-flash", description="Model to use for extraction")
    api_key: Optional[str] = Field(default=None, description="API key (optional if set in environment)")
    extraction_passes: int = Field(default=1, description="Number of extraction passes")
    max_workers: int = Field(default=10, description="Maximum number of parallel workers")
    max_char_buffer: int = Field(default=2000, description="Maximum character buffer size")

@app.get("/")
async def root():
    return {
        "message": "LangExtract API is running",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.post("/extract")
async def extract_entities(req: ExtractRequest):
    try:
        # 检查API密钥
        api_key = req.api_key or os.getenv("LANGEXTRACT_API_KEY")
        if not api_key and req.model_id.startswith("gemini"):
            raise HTTPException(
                status_code=400,
                detail="API key is required for Gemini models. Set LANGEXTRACT_API_KEY environment variable or provide api_key in request."
            )

        # 转换examples为langextract格式
        lx_examples = []
        for example in req.examples:
            lx_extractions = []
            for extraction in example.extractions:
                lx_extraction = lx.data.Extraction(
                    extraction_class=extraction.extraction_class,
                    extraction_text=extraction.extraction_text,
                    attributes=extraction.attributes
                )
                lx_extractions.append(lx_extraction)

            lx_example = lx.data.ExampleData(
                text=example.text,
                extractions=lx_extractions
            )
            lx_examples.append(lx_example)

        logger.info(f"Starting extraction with model: {req.model_id}")

        # 执行提取
        result = lx.extract(
            text_or_documents=req.text,
            prompt_description=req.prompt_description,
            examples=lx_examples,
            model_id=req.model_id,
            api_key=api_key,
            extraction_passes=req.extraction_passes,
            max_workers=req.max_workers,
            max_char_buffer=req.max_char_buffer
        )

        logger.info("Extraction completed successfully")

        # 转换结果为可序列化的格式
        return {
            "success": True,
            "result": {
                "document_id": getattr(result, 'document_id', None),
                "text": getattr(result, 'text', req.text),
                "extractions": [
                    {
                        "extraction_class": ext.extraction_class,
                        "extraction_text": ext.extraction_text,
                        "attributes": ext.attributes,
                        "start_char": getattr(ext, 'start_char', None),
                        "end_char": getattr(ext, 'end_char', None)
                    }
                    for ext in getattr(result, 'extractions', [])
                ]
            }
        }

    except Exception as e:
        logger.error(f"Extraction failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Extraction failed: {str(e)}")

@app.post("/extract/simple")
async def extract_simple(
    text: str = Field(..., description="Text to extract from"),
    prompt: str = Field(..., description="What to extract"),
    model_id: str = Field(default="gemini-2.5-flash", description="Model to use")
):
    """
    简化的提取端点，使用预定义的示例
    """
    try:
        # 使用一个通用的示例
        example_extractions = [
            lx.data.Extraction(
                extraction_class="entity",
                extraction_text="example text",
                attributes={"type": "general"}
            )
        ]

        example = lx.data.ExampleData(
            text="This is an example text with entities.",
            extractions=example_extractions
        )

        api_key = os.getenv("LANGEXTRACT_API_KEY")
        if not api_key and model_id.startswith("gemini"):
            raise HTTPException(
                status_code=400,
                detail="API key is required for Gemini models. Set LANGEXTRACT_API_KEY environment variable."
            )

        result = lx.extract(
            text_or_documents=text,
            prompt_description=prompt,
            examples=[example],
            model_id=model_id,
            api_key=api_key
        )

        return {
            "success": True,
            "result": {
                "text": text,
                "extractions": [
                    {
                        "extraction_class": ext.extraction_class,
                        "extraction_text": ext.extraction_text,
                        "attributes": ext.attributes,
                        "start_char": getattr(ext, 'start_char', None),
                        "end_char": getattr(ext, 'end_char', None)
                    }
                    for ext in getattr(result, 'extractions', [])
                ]
            }
        }

    except Exception as e:
        logger.error(f"Simple extraction failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Simple extraction failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
